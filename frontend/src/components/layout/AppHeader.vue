<script setup>
import { computed, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '../../stores/user'
import { ElMessageBox, ElMessage } from 'element-plus'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 移动端菜单显示状态
const mobileMenuVisible = ref(false)

// 导航菜单
const navMenus = [
  { name: '外汇技术指标分析', path: '/', shortName: '技术指标' },
  { name: '外汇财务仪表盘', path: '/finance-dashboard', shortName: '财务仪表盘' },
  { name: '风险管理驾驶舱', path: '/risk-dashboard', shortName: '风险管理' }
]

// 当前激活的路由
const activeRoute = computed(() => route.path)

// 导航到指定页面
const navigateTo = (path) => {
  router.push(path)
  // 移动端导航后关闭菜单
  mobileMenuVisible.value = false
}

// 切换移动端菜单
const toggleMobileMenu = () => {
  mobileMenuVisible.value = !mobileMenuVisible.value
}

// 处理退出登录
const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userStore.logout()
    ElMessage({
      type: 'success',
      message: '退出登录成功'
    })
    router.push('/login')
  }).catch(() => {
    // 取消退出操作
  })
}
</script>

<template>
  <div class="app-header">
    <!-- 头部导航 -->
    <div class="header-container">
      <div class="logo-container" @click="navigateTo('/')">
        <i class="fas fa-chart-line"></i>
        <span class="logo-text">外汇平台</span>
      </div>

      <!-- 桌面端导航菜单 -->
      <div class="nav-menu desktop-only">
        <div
          v-for="(menu, index) in navMenus"
          :key="index"
          class="nav-item"
          :class="{ active: menu.path === activeRoute }"
          @click="navigateTo(menu.path)"
        >
          {{ menu.name }}
        </div>
      </div>

      <!-- 移动端汉堡菜单按钮 -->
      <div class="mobile-menu-button mobile-only" @click="toggleMobileMenu">
        <i class="fas fa-bars" v-if="!mobileMenuVisible"></i>
        <i class="fas fa-times" v-else></i>
      </div>

      <!-- 右侧用户信息等 -->
      <div class="header-right desktop-only">
        <el-dropdown>
          <span class="user-info">
            <i class="fas fa-user-circle"></i>
            <span class="user-name">{{ userStore.currentUsername || '用户' }}</span>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item divided @click="handleLogout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 移动端下拉菜单 -->
    <div class="mobile-menu" :class="{ 'mobile-menu-visible': mobileMenuVisible }">
      <div class="mobile-nav-items">
        <div
          v-for="(menu, index) in navMenus"
          :key="index"
          class="mobile-nav-item"
          :class="{ active: menu.path === activeRoute }"
          @click="navigateTo(menu.path)"
        >
          <span>{{ menu.shortName }}</span>
        </div>
      </div>
      <div class="mobile-user-section">
        <div class="mobile-user-info">
          <i class="fas fa-user-circle"></i>
          <span>{{ userStore.currentUsername || '用户' }}</span>
        </div>
        <div class="mobile-logout" @click="handleLogout">
          <i class="fas fa-sign-out-alt"></i>
          <span>退出登录</span>
        </div>
      </div>
    </div>

    <!-- 移动端遮罩层 -->
    <div
      class="mobile-overlay"
      :class="{ 'mobile-overlay-visible': mobileMenuVisible }"
      @click="mobileMenuVisible = false">
    </div>
  </div>
</template>

<style scoped>
.app-header {
  width: 100%;
  height: 60px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.header-container {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 20px;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

.logo-container {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: #165DFF;
  cursor: pointer;
  transition: color 0.3s;
}

.logo-container:hover {
  color: #0040CC;
}

.logo-container i {
  margin-right: 8px;
}

.logo-text {
  white-space: nowrap;
}

.nav-menu {
  display: flex;
  margin-left: 40px;
  flex: 1;
}

.nav-item {
  padding: 0 16px;
  height: 60px;
  line-height: 60px;
  color: #333;
  cursor: pointer;
  transition: color 0.3s;
  white-space: nowrap;
}

.nav-item:hover {
  color: #165DFF;
}

.nav-item.active {
  color: #165DFF;
  font-weight: bold;
  position: relative;
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 16px;
  right: 16px;
  height: 2px;
  background-color: #165DFF;
}

.mobile-menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  cursor: pointer;
  color: #333;
  transition: color 0.3s;
  margin-left: auto;
}

.mobile-menu-button:hover {
  color: #165DFF;
}

.mobile-menu-button i {
  font-size: 18px;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.user-info i {
  font-size: 20px;
  margin-right: 8px;
  color: #606266;
}

.user-name {
  white-space: nowrap;
}

/* 移动端菜单样式 */
.mobile-menu {
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-100%);
  transition: transform 0.3s ease;
  z-index: 999;
  max-height: calc(100vh - 60px);
  overflow-y: auto;
  display: none;
}

.mobile-menu-visible {
  transform: translateY(0);
}

.mobile-nav-items {
  padding: 16px 0;
}

.mobile-nav-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  color: #333;
  cursor: pointer;
  transition: background-color 0.3s;
  border-bottom: 1px solid #f0f0f0;
}

.mobile-nav-item:hover {
  background-color: #f5f7fa;
}

.mobile-nav-item.active {
  color: #165DFF;
  background-color: rgba(22, 93, 255, 0.05);
  font-weight: 500;
}

.mobile-nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #165DFF;
}

.mobile-user-section {
  border-top: 1px solid #e8e8e8;
  padding: 16px 0;
}

.mobile-user-info {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #666;
}

.mobile-user-info i {
  font-size: 18px;
  margin-right: 12px;
  color: #999;
}

.mobile-logout {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #ff4d4f;
  cursor: pointer;
  transition: background-color 0.3s;
}

.mobile-logout:hover {
  background-color: rgba(255, 77, 79, 0.05);
}

.mobile-logout i {
  font-size: 16px;
  margin-right: 12px;
}

/* 移动端遮罩层 */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
  z-index: 998;
  display: none;
}

.mobile-overlay-visible {
  opacity: 1;
  visibility: visible;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-container {
    padding: 0 16px;
  }

  .nav-menu {
    margin-left: 24px;
  }

  .nav-item {
    padding: 0 12px;
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .app-header {
    height: var(--mobile-header-height, 56px);
  }

  .header-container {
    height: var(--mobile-header-height, 56px);
    padding: 0 var(--mobile-padding, 12px);
  }

  .logo-container {
    font-size: 16px;
  }

  .logo-text {
    display: none;
  }

  .mobile-menu-button {
    display: flex;
  }

  .mobile-menu {
    display: block;
    top: var(--mobile-header-height, 56px);
  }

  .mobile-overlay {
    display: block;
  }

  .mobile-nav-item {
    padding: 14px var(--mobile-padding, 12px);
    font-size: 15px;
  }

  .mobile-user-info,
  .mobile-logout {
    padding: 12px var(--mobile-padding, 12px);
  }
}

@media (max-width: 480px) {
  .logo-container i {
    margin-right: 4px;
  }

  .mobile-nav-item {
    padding: 12px var(--mobile-padding, 12px);
  }

  .mobile-user-section {
    padding: 12px 0;
  }
}
</style>
