/* 全局样式 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  width: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  color: #333;
  background-color: #f5f7fa;
}

/* 公共颜色变量 */
:root {
  --primary-color: #165DFF;
  --success-color: #00B42A;
  --warning-color: #FAAD14;
  --danger-color: #F53F3F;
  --info-color: #86909C;

  --background-color: #f5f7fa;
  --card-background: #fff;

  --border-color: #e4e7ed;
  --text-primary: #333;
  --text-regular: #606266;
  --text-secondary: #909399;
}

/* 常用布局类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

/* 字体大小类 */
.text-xs {
  font-size: 12px;
}

.text-sm {
  font-size: 14px;
}

.text-base {
  font-size: 16px;
}

.text-lg {
  font-size: 18px;
}

.text-xl {
  font-size: 20px;
}

.text-2xl {
  font-size: 24px;
}

/* 边距类 */
.m-1 {
  margin: 4px;
}

.m-2 {
  margin: 8px;
}

.m-3 {
  margin: 12px;
}

.m-4 {
  margin: 16px;
}

.mt-1 {
  margin-top: 4px;
}

.mt-2 {
  margin-top: 8px;
}

.mt-3 {
  margin-top: 12px;
}

.mt-4 {
  margin-top: 16px;
}

.mb-1 {
  margin-bottom: 4px;
}

.mb-2 {
  margin-bottom: 8px;
}

.mb-3 {
  margin-bottom: 12px;
}

.mb-4 {
  margin-bottom: 16px;
}

/* 卡片样式 */
.card {
  background-color: var(--card-background);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 16px;
}

/* 移动端专用变量 */
:root {
  --mobile-padding: 12px;
  --mobile-margin: 8px;
  --mobile-font-size: 14px;
  --mobile-header-height: 56px;
  --tablet-padding: 16px;
  --tablet-margin: 12px;
}

/* 响应式工具类 */
.mobile-only {
  display: none;
}

.desktop-only {
  display: block;
}

.tablet-only {
  display: none;
}

/* 移动端专用布局类 */
.mobile-stack {
  display: flex;
  flex-direction: column;
  gap: var(--mobile-margin);
}

.mobile-grid-1 {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--mobile-margin);
}

.mobile-grid-2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--mobile-margin);
}

.mobile-padding {
  padding: var(--mobile-padding);
}

.mobile-margin {
  margin: var(--mobile-margin);
}

/* 触摸友好的按钮 */
.touch-friendly {
  min-height: 44px;
  min-width: 44px;
  padding: 12px 16px;
}

/* 响应式断点 */
@media (max-width: 1200px) {
  .container {
    max-width: 100%;
    padding: 0 var(--tablet-padding);
  }

  .tablet-only {
    display: block;
  }
}

@media (max-width: 768px) {
  .hide-on-mobile {
    display: none;
  }

  .mobile-only {
    display: block;
  }

  .desktop-only,
  .tablet-only {
    display: none;
  }

  /* 移动端字体调整 */
  body {
    font-size: var(--mobile-font-size);
  }

  .text-xs {
    font-size: 11px;
  }

  .text-sm {
    font-size: 13px;
  }

  .text-base {
    font-size: 14px;
  }

  .text-lg {
    font-size: 16px;
  }

  .text-xl {
    font-size: 18px;
  }

  .text-2xl {
    font-size: 20px;
  }

  /* 移动端卡片样式 */
  .card {
    padding: var(--mobile-padding);
    margin-bottom: var(--mobile-margin);
  }

  /* 移动端间距调整 */
  .m-1 { margin: 2px; }
  .m-2 { margin: 4px; }
  .m-3 { margin: 6px; }
  .m-4 { margin: 8px; }

  .mt-1 { margin-top: 2px; }
  .mt-2 { margin-top: 4px; }
  .mt-3 { margin-top: 6px; }
  .mt-4 { margin-top: 8px; }

  .mb-1 { margin-bottom: 2px; }
  .mb-2 { margin-bottom: 4px; }
  .mb-3 { margin-bottom: 6px; }
  .mb-4 { margin-bottom: 8px; }
}

@media (max-width: 480px) {
  /* 超小屏幕优化 */
  .container {
    padding: 0 var(--mobile-padding);
  }

  .card {
    padding: 8px;
    border-radius: 6px;
  }

  .text-2xl {
    font-size: 18px;
  }

  .text-xl {
    font-size: 16px;
  }
}
