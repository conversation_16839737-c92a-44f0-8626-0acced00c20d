<template>
  <div class="login-container">
    <div class="login-box">
      <div class="logo-wrapper">
        <h1 class="title">外汇平台分析系统</h1>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        status-icon
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            autocomplete="off"
          >
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
            autocomplete="off"
            @keyup.enter="handleLogin"
          >
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item>
          <el-button
            :loading="loading"
            type="primary"
            class="login-button"
            @click="handleLogin"
          >
            登录
          </el-button>
        </el-form-item>
      </el-form>

      <div class="login-tips">
        <p>用户名: admin</p>
        <p>密码: 123</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import { useUserStore } from '../stores/user'

// 路由实例
const router = useRouter()
// 用户Store
const userStore = useUserStore()
// 加载状态
const loading = ref(false)
// 表单引用
const loginFormRef = ref(null)
// 表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 表单校验规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 3, message: '密码长度最少为3个字符', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true

      try {
        // 调用登录方法
        await userStore.login({
          username: loginForm.username,
          password: loginForm.password
        })

        ElMessage({
          type: 'success',
          message: '登录成功'
        })

        // 登录成功后跳转到首页
        router.push('/')
      } catch (error) {
        ElMessage({
          type: 'error',
          message: error.message || '登录失败'
        })
      } finally {
        loading.value = false
      }
    }
  })
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px;
  box-sizing: border-box;
}

.login-box {
  width: 100%;
  max-width: 400px;
  padding: 30px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.logo-wrapper {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  font-size: 24px;
  color: #409EFF;
  margin: 0;
}

.login-form {
  margin-bottom: 20px;
}

.login-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
}

.login-tips {
  margin-top: 20px;
  font-size: 14px;
  color: #909399;
}

.login-tips p {
  margin: 5px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-container {
    padding: var(--mobile-padding, 12px);
    align-items: flex-start;
    padding-top: 10vh;
  }

  .login-box {
    padding: 24px;
    border-radius: 6px;
  }

  .title {
    font-size: 20px;
  }

  .logo-wrapper {
    margin-bottom: 24px;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 8px;
    padding-top: 8vh;
  }

  .login-box {
    padding: 20px;
  }

  .title {
    font-size: 18px;
  }

  .logo-wrapper {
    margin-bottom: 20px;
  }

  .login-form {
    margin-bottom: 16px;
  }
}

/* 移动端输入框优化 */
@media (max-width: 768px) {
  :deep(.el-input__inner) {
    height: 44px;
    font-size: 16px;
  }

  :deep(.el-form-item) {
    margin-bottom: 20px;
  }

  :deep(.el-form-item__content) {
    line-height: 44px;
  }
}
</style>
